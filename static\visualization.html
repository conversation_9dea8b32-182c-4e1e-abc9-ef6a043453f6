<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="titles.dataVisualization">数据可视化 - 文档分析系统</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        /* 页面特定的额外样式 */
        .raw-data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .raw-data-header h3 {
            margin: 0;
            color: var(--text-primary);
        }

        /* 可视化页面特定样式 */
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-sm);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .summary-card .value {
            font-size: 2em;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .summary-card .label {
            color: var(--text-muted);
            font-size: 0.9em;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .network-chart {
            height: 500px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .task-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .task-tab {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            cursor: pointer;
            transition: var(--transition);
            background: var(--bg-primary);
            color: var(--text-secondary);
        }
        
        .task-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .task-tab:hover {
            border-color: var(--primary-color);
        }

        .export-controls {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .control-row {
                grid-template-columns: 1fr;
            }
            
            .summary-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1 data-i18n="visualization.title">📊 分析结果可视化</h1>
            <p class="subtitle" data-i18n="visualization.description">将复杂的分析结果转换为直观的图表展示</p>
            <nav class="main-nav">
                <a href="/" class="nav-link" data-i18n="nav.home">🏠 文档分析</a>
                <a href="/static/visualization.html" class="nav-link active" data-i18n="nav.visualization">📊 数据可视化</a>
                <a href="/static/data-table.html" class="nav-link" data-i18n="nav.tableView">📋 表格视图</a>
                <a href="/static/document-management.html" class="nav-link" data-i18n="nav.documentManagement">📁 文档管理</a>
                <a href="/static/prompts.html" class="nav-link" data-i18n="nav.promptEditor">🔧 提示词编辑器</a>
                <a href="/docs" class="nav-link" data-i18n="nav.apiDocs">📚 API文档</a>
                <button class="language-toggle" onclick="toggleLanguage()" title="Switch Language">EN</button>
                <button type="button" class="theme-toggle" onclick="toggleTheme()" data-i18n="analysis.switchTheme" title="Switch Theme">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main>

            <!-- 控制面板 -->
            <section class="config-section control-panel-section">
                <h2 data-i18n="visualization.controls">🎛️ Control Panel</h2>

                <!-- 文档选择区域 -->
                <div class="document-selection-section">
                    <div class="config-grid">
                        <div class="form-group">
                            <label for="doc-select" data-i18n="visualization.selectDocument">Select Document</label>
                            <select id="doc-select" onchange="onDocumentSelect()">
                                <option value="" data-i18n="analysis.selectDocument">-- 选择已有文档 --</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="doc-id" data-i18n="analysis.documentId">或手动输入文档ID</label>
                            <input type="text" id="doc-id" data-i18n="analysis.documentId" placeholder="输入文档ID" value="">
                        </div>
                    </div>
                </div>

                <!-- 分析任务选择区域 -->
                <div class="analysis-task-section">
                    <div class="form-group">
                        <label for="task-filter" data-i18n="visualization.analysisTask">Analysis Task</label>
                        <div class="task-selector-container">
                            <div class="task-selector-header">
                                <small class="hint" data-i18n="visualization.taskHint">选择要显示的分析任务类型</small>
                                <button type="button" class="select-all-btn" onclick="toggleSelectAllTasks()" data-i18n="visualization.selectAllTasks">Deselect All</button>
                            </div>
                            <select id="task-filter" multiple>
                                <option value="actor_relation" data-i18n="taskTypes.actorRelation" selected>Actor Relations</option>
                                <option value="role_framing" data-i18n="taskTypes.roleFraming" selected>Role Framing</option>
                                <option value="problem_scope" data-i18n="taskTypes.problemScope" selected>Problem Scope</option>
                                <option value="causal_mechanism" data-i18n="taskTypes.causalMechanism" selected>Causal Mechanism</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="action-buttons-section">
                    <div class="analysis-buttons action-buttons">
                        <button type="button" class="tertiary-btn" onclick="loadVisualization()" data-i18n="visualization.loadVisualization">
                            📊 LOAD VISUALIZATION
                        </button>
                        <button type="button" class="tertiary-btn" onclick="loadSampleData()" data-i18n="visualization.loadSampleData">
                            🎯 Load Sample Data
                        </button>
                        <button type="button" class="tertiary-btn" onclick="toggleRawData()" data-i18n="visualization.viewRawData">
                            📄 View Raw Data
                        </button>
                        <button type="button" class="tertiary-btn" onclick="window.open('/static/data-table.html', '_blank')" data-i18n="visualization.tableView">
                            📋 Table View
                        </button>
                    </div>
                </div>
            </section>

            <!-- 摘要统计 -->
            <section class="config-section">
                <h2 data-i18n="visualization.summary">📊 分析摘要</h2>
                <div class="summary-cards" id="summary-cards">
                    <div class="summary-card">
                        <div class="value" id="total-actors">-</div>
                        <div class="label" data-i18n="visualization.totalActors">行为者总数</div>
                    </div>
                    <div class="summary-card">
                        <div class="value" id="total-relations">-</div>
                        <div class="label" data-i18n="visualization.totalRelations">关系总数</div>
                    </div>
                    <div class="summary-card">
                        <div class="value" id="complexity-score">-</div>
                        <div class="label" data-i18n="visualization.complexityScore">复杂度评分</div>
                    </div>
                    <div class="summary-card">
                        <div class="value" id="task-count">-</div>
                        <div class="label" data-i18n="visualization.taskCount">分析任务数</div>
                    </div>
                </div>
            </section>

            <!-- 可视化内容 -->
            <section class="config-section">
                <h2 data-i18n="visualization.charts">📈 可视化图表</h2>

                <!-- 任务标签 -->
                <div class="task-tabs" id="task-tabs">
                    <div class="task-tab active" data-task="overview" onclick="switchTask('overview')" data-i18n="visualization.overview">总览</div>
                    <div class="task-tab" data-task="actor_relation" onclick="switchTask('actor_relation')" data-i18n="taskTypes.actorRelation">行为者关系</div>
                    <div class="task-tab" data-task="role_framing" onclick="switchTask('role_framing')" data-i18n="taskTypes.roleFraming">角色塑造</div>
                    <div class="task-tab" data-task="problem_scope" onclick="switchTask('problem_scope')" data-i18n="taskTypes.problemScope">问题范围</div>
                    <div class="task-tab" data-task="causal_mechanism" onclick="switchTask('causal_mechanism')" data-i18n="taskTypes.causalMechanism">因果机制</div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-grid" id="charts-container">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p data-i18n="visualization.selectDocumentPrompt">请选择文档并加载可视化数据</p>
                    </div>
                </div>
            </section>

            <!-- 原始数据显示区域 -->
            <section class="config-section" id="raw-data-section" style="display: none;">
                <div class="raw-data-header">
                    <h3 data-i18n="visualization.rawData">📄 原始响应数据</h3>
                    <div class="export-controls">
                        <button class="secondary-btn" onclick="copyRawData()" data-i18n="visualization.copyData">📋 复制数据</button>
                        <button class="secondary-btn" onclick="exportData('json')" data-i18n="visualization.exportJson">📄 导出JSON</button>
                        <button class="secondary-btn" onclick="exportData('csv')" data-i18n="visualization.exportCsv">📊 导出CSV</button>
                    </div>
                </div>
                <div class="raw-data-container">
                    <pre id="raw-data-content" class="raw-data-content"></pre>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2024 文档分析系统 | Powered by 智谱AI</p>
        </footer>
    </div>

    <script src="/static/js/i18n.js"></script>
    <script>
        let currentData = null;
        let charts = {};

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            loadDocumentList();

            // 检查是否从分析页面跳转过来
            const fromAnalysis = sessionStorage.getItem('fromAnalysis');
            const currentDocId = sessionStorage.getItem('currentDocId');

            if (fromAnalysis === 'true' && currentDocId) {
                // 自动加载当前文档的可视化
                document.getElementById('doc-id').value = currentDocId;
                loadVisualization();

                // 清除sessionStorage中的标记
                sessionStorage.removeItem('fromAnalysis');
                sessionStorage.removeItem('currentDocId');
            } else {
                // 否则加载示例数据
                loadSampleData();
            }
        });

        function initializeEventListeners() {
            // 设置任务筛选器事件
            document.getElementById('task-filter').addEventListener('change', function() {
                updateSelectAllButtonState();
                if (currentData) {
                    renderVisualization(currentData);
                }
            });

            // 初始化全选按钮状态
            updateSelectAllButtonState();
        }

        async function loadDocumentList() {
            try {
                const response = await fetch('/api/v1/documents');
                if (response.ok) {
                    const documents = await response.json();
                    const select = document.getElementById('doc-select');

                    // 清空现有选项（保留默认选项）
                    select.innerHTML = '<option value="">-- 选择已有文档 --</option>';

                    // 添加文档选项
                    documents.forEach(doc => {
                        const option = document.createElement('option');
                        option.value = doc.doc_id;
                        option.textContent = `${doc.title || doc.doc_id} (${doc.doc_id})`;
                        select.appendChild(option);
                    });
                } else {
                    console.error('获取文档列表失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载文档列表失败:', error);
            }
        }

        function onDocumentSelect() {
            const select = document.getElementById('doc-select');
            const docIdInput = document.getElementById('doc-id');

            if (select.value) {
                docIdInput.value = select.value;
            }
        }

        async function loadVisualization() {
            const docId = document.getElementById('doc-id').value.trim();
            if (!docId) {
                alert(t('errors.noDocumentSelected'));
                return;
            }

            showLoading();
            
            try {
                const taskFilter = Array.from(document.getElementById('task-filter').selectedOptions)
                    .map(option => option.value);
                
                const params = new URLSearchParams();
                if (taskFilter.length > 0) {
                    taskFilter.forEach(task => params.append('task_types', task));
                }
                // 添加语言参数
                params.append('lang', currentLanguage);

                const response = await fetch(`/api/v1/visualization/data/${docId}?${params}`);
                if (response.ok) {
                    currentData = await response.json();
                    renderVisualization(currentData);
                } else {
                    const error = await response.json();
                    showError(`加载可视化数据失败: ${error.detail || '未知错误'}`);
                }
            } catch (error) {
                console.error('加载可视化数据失败:', error);
                showError('加载可视化数据失败');
            }
        }

        function loadSampleData() {
            // 加载示例数据用于演示
            const sampleData = {
                doc_id: "sample_doc_001",
                title: "示例政策文档",
                analysis_date: new Date().toISOString(),
                task_types: ["actor_relation", "role_framing"],
                charts: {
                    actor_relation: [
                        {
                            type: "pie",
                            title: "行为者类型分布",
                            data: {
                                labels: ["政府机构", "企业", "公众", "社会组织"],
                                datasets: [{
                                    data: [5, 3, 2, 1],
                                    backgroundColor: ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"]
                                }]
                            }
                        },
                        {
                            type: "bar",
                            title: "关系类型分布",
                            data: {
                                labels: ["监管关系", "合作关系", "对立关系", "服务关系"],
                                datasets: [{
                                    label: "关系数量",
                                    data: [8, 4, 2, 3],
                                    backgroundColor: "#36A2EB"
                                }]
                            }
                        }
                    ],
                    role_framing: [
                        {
                            type: "pie",
                            title: "角色分布",
                            data: {
                                labels: ["英雄", "受害者", "反派"],
                                datasets: [{
                                    data: [3, 2, 1],
                                    backgroundColor: ["#28a745", "#ffc107", "#dc3545"]
                                }]
                            }
                        },
                        {
                            type: "radar",
                            title: "角色特征分析",
                            data: {
                                labels: ["积极性", "影响力", "描述详细度", "情感强度"],
                                datasets: [
                                    {
                                        label: "英雄",
                                        data: [85, 75, 80, 90],
                                        borderColor: "#28a745",
                                        backgroundColor: "rgba(40, 167, 69, 0.2)"
                                    },
                                    {
                                        label: "受害者",
                                        data: [30, 60, 70, 40],
                                        borderColor: "#ffc107",
                                        backgroundColor: "rgba(255, 193, 7, 0.2)"
                                    }
                                ]
                            }
                        }
                    ]
                },
                summary: {
                    total_actors: 11,
                    total_relations: 17,
                    complexity_score: 0.8,
                    key_themes: ["政策监管", "企业发展", "公众参与"]
                }
            };
            
            currentData = sampleData;
            renderVisualization(currentData);
        }

        function renderVisualization(data) {
            updateSummaryCards(data.summary);
            updateTaskTabs(data.task_types);
            renderCharts(data.charts);
        }

        function updateSummaryCards(summary) {
            document.getElementById('total-actors').textContent = summary.total_actors || 0;
            document.getElementById('total-relations').textContent = summary.total_relations || 0;
            document.getElementById('complexity-score').textContent = 
                ((summary.complexity_score || 0) * 100).toFixed(0) + '%';
            document.getElementById('task-count').textContent = 
                Object.keys(currentData.charts || {}).length;
        }

        function updateTaskTabs(taskTypes) {
            const container = document.getElementById('task-tabs');
            const selectedTasks = Array.from(document.getElementById('task-filter').selectedOptions)
                .map(option => option.value);
            
            const tasksToShow = selectedTasks.length > 0 ? selectedTasks : taskTypes;
            
            container.innerHTML = tasksToShow.map(task => `
                <div class="task-tab active" data-task="${task}">
                    ${getTaskTypeName(task)}
                </div>
            `).join('');
        }

        function renderCharts(chartsData) {
            const container = document.getElementById('charts-container');
            const selectedTasks = Array.from(document.getElementById('task-filter').selectedOptions)
                .map(option => option.value);
            
            const tasksToShow = selectedTasks.length > 0 ? selectedTasks : Object.keys(chartsData);
            
            // 清除现有图表
            Object.values(charts).forEach(chart => chart.destroy());
            charts = {};
            
            let html = '';
            
            tasksToShow.forEach(taskType => {
                const taskCharts = chartsData[taskType] || [];
                taskCharts.forEach((chartConfig, index) => {
                    const chartId = `chart-${taskType}-${index}`;
                    html += `
                        <div class="chart-container">
                            <div class="chart-title">${chartConfig.title}</div>
                            <div class="chart-wrapper">
                                <canvas id="${chartId}"></canvas>
                            </div>
                        </div>
                    `;
                });
            });
            
            if (html === '') {
                html = `
                    <div class="empty-state">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4zm2.5 2.1h-15V5h15v14.1zm0-16.1h-15c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h15c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                        </svg>
                        <h3>暂无图表数据</h3>
                        <p>请选择分析任务或检查文档分析结果</p>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            
            // 渲染图表
            setTimeout(() => {
                tasksToShow.forEach(taskType => {
                    const taskCharts = chartsData[taskType] || [];
                    taskCharts.forEach((chartConfig, index) => {
                        const chartId = `chart-${taskType}-${index}`;
                        const canvas = document.getElementById(chartId);
                        if (canvas) {
                            charts[chartId] = createChart(canvas, chartConfig);
                        }
                    });
                });
            }, 100);
        }

        function createChart(canvas, config) {
            // 处理网络图
            if (config.type === 'network') {
                return createNetworkChart(canvas, config);
            }

            const ctx = canvas.getContext('2d');

            const chartConfig = {
                type: config.type,
                data: config.data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                title: function(context) {
                                    return config.title + ' - ' + context[0].label;
                                },
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    label += context.parsed.y || context.parsed;

                                    // 添加百分比信息（对于饼图）
                                    if (config.type === 'pie' || config.type === 'doughnut') {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        label += ` (${percentage}%)`;
                                    }

                                    return label;
                                },
                                afterLabel: function(context) {
                                    // 添加额外的详细信息
                                    const extraInfo = [];

                                    if (config.detailData && config.detailData[context.dataIndex]) {
                                        const details = config.detailData[context.dataIndex];
                                        if (details.description) extraInfo.push(`描述: ${details.description}`);
                                        if (details.count) extraInfo.push(`数量: ${details.count}`);
                                        if (details.examples) extraInfo.push(`示例: ${details.examples.slice(0, 2).join(', ')}`);
                                    }

                                    return extraInfo;
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            };

            // 特殊配置
            if (config.type === 'radar') {
                chartConfig.options.scales = {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                };
            } else if (config.type === 'bar') {
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true
                    }
                };
            }

            return new Chart(ctx, chartConfig);
        }

        function createNetworkChart(canvas, config) {
            // 清除canvas内容并创建网络图容器
            const container = canvas.parentElement;
            container.innerHTML = `<div id="${canvas.id}-network" style="width: 100%; height: 400px;"></div>`;

            const networkContainer = document.getElementById(`${canvas.id}-network`);

            // 准备网络图数据
            const nodes = new vis.DataSet(config.data.nodes.map((node, index) => {
                // 构建简洁的悬停信息
                let tooltip = `名称: ${node.label || node.id}\n类型: ${node.type || '未知'}`;

                if (node.actions && node.actions.length > 0) {
                    tooltip += `\n行动数量: ${node.actions.length}`;
                    const mainActions = node.actions.slice(0, 2).map(action =>
                        typeof action === 'string' ? action : (action.description || action.name || '未知行动')
                    );
                    if (mainActions.length > 0) {
                        tooltip += `\n主要行动: ${mainActions.join(', ')}`;
                    }
                }

                if (node.description) {
                    tooltip += `\n描述: ${node.description.substring(0, 100)}${node.description.length > 100 ? '...' : ''}`;
                }

                return {
                    id: index,
                    label: node.label || node.id,
                    title: tooltip,
                    color: getNodeColor(node.type),
                    font: { size: 12 }
                };
            }));

            const edges = new vis.DataSet(config.data.edges.map((edge, index) => {
                const fromIndex = config.data.nodes.findIndex(n => n.id === edge.from || n.label === edge.from);
                const toIndex = config.data.nodes.findIndex(n => n.id === edge.to || n.label === edge.to);
                const fromNode = config.data.nodes[fromIndex];
                const toNode = config.data.nodes[toIndex];

                // 构建简洁的关系悬停信息
                let tooltip = `关系: ${fromNode ? fromNode.label || fromNode.id : '未知'} → ${toNode ? toNode.label || toNode.id : '未知'}`;
                tooltip += `\n类型: ${edge.label || edge.type || '未知'}`;

                if (edge.description) {
                    tooltip += `\n描述: ${edge.description.substring(0, 80)}${edge.description.length > 80 ? '...' : ''}`;
                }

                if (edge.strength) {
                    tooltip += `\n强度: ${edge.strength}`;
                }

                if (edge.evidence && edge.evidence.length > 0) {
                    tooltip += `\n证据数量: ${edge.evidence.length}`;
                }

                return {
                    id: index,
                    from: fromIndex >= 0 ? fromIndex : 0,
                    to: toIndex >= 0 ? toIndex : 0,
                    label: edge.label || '',
                    title: tooltip,
                    arrows: 'to',
                    color: { color: '#848484' }
                };
            }));

            const data = { nodes: nodes, edges: edges };

            const options = {
                nodes: {
                    shape: 'dot',
                    size: 16,
                    font: {
                        size: 12,
                        color: '#000000'
                    },
                    borderWidth: 2
                },
                edges: {
                    width: 2,
                    color: { inherit: 'from' },
                    smooth: {
                        type: 'continuous'
                    }
                },
                physics: {
                    stabilization: { iterations: 100 }
                },
                interaction: {
                    hover: true,
                    tooltipDelay: 200
                }
            };

            return new vis.Network(networkContainer, data, options);
        }

        function getNodeColor(type) {
            const colors = {
                '政府机构': '#FF6384',
                '企业': '#36A2EB',
                '公众': '#FFCE56',
                '社会组织': '#4BC0C0',
                '未知': '#9966FF'
            };
            return colors[type] || colors['未知'];
        }

        function getTaskTypeName(taskType) {
            const names = {
                'actor_relation': '行为者关系',
                'role_framing': '角色塑造',
                'problem_scope': '问题范围',
                'causal_mechanism': '因果机制'
            };
            return names[taskType] || taskType;
        }

        function showLoading() {
            document.getElementById('charts-container').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在加载可视化数据...</p>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('charts-container').innerHTML = `
                <div class="error-message">
                    <strong>错误:</strong> ${message}
                </div>
            `;
        }

        function toggleRawData() {
            const rawDataContainer = document.getElementById('raw-data-container');
            const chartsContainer = document.getElementById('charts-container');
            const toggleBtn = event.target;

            if (rawDataContainer.style.display === 'none') {
                // 显示原始数据
                if (currentData) {
                    document.getElementById('raw-data-content').textContent = JSON.stringify(currentData, null, 2);
                } else {
                    document.getElementById('raw-data-content').textContent = t('errors.noDataAvailable');
                }
                rawDataContainer.style.display = 'block';
                chartsContainer.style.display = 'none';
                toggleBtn.textContent = t('visualization.viewCharts');
            } else {
                // 显示图表
                rawDataContainer.style.display = 'none';
                chartsContainer.style.display = 'block';
                toggleBtn.textContent = t('visualization.viewRawData');
            }
        }

        function copyRawData() {
            const rawDataContent = document.getElementById('raw-data-content').textContent;
            if (rawDataContent && rawDataContent !== t('errors.noDataAvailable')) {
                navigator.clipboard.writeText(rawDataContent).then(() => {
                    alert(t('success.dataCopied'));
                }).catch(err => {
                    console.error('复制失败:', err);
                    alert(t('errors.dataLoadFailed'));
                });
            } else {
                alert(t('errors.noDataAvailable'));
            }
        }

        async function exportData(format) {
            if (!currentData) {
                alert(t('table.noDataToExport'));
                return;
            }

            try {
                const response = await fetch(`/api/v1/visualization/export/${currentData.doc_id}?format=${format}`);
                if (response.ok) {
                    const result = await response.json();

                    if (format === 'json') {
                        const blob = new Blob([JSON.stringify(result.data, null, 2)], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `visualization_${currentData.doc_id}.json`;
                        a.click();
                        URL.revokeObjectURL(url);
                    } else if (format === 'csv') {
                        const blob = new Blob([result.data], { type: 'text/csv' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `visualization_${currentData.doc_id}.csv`;
                        a.click();
                        URL.revokeObjectURL(url);
                    }

                    alert('导出成功');
                } else {
                    alert('导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败');
            }
        }

        // 全选/取消全选任务
        function toggleSelectAllTasks() {
            const taskFilter = document.getElementById('task-filter');
            const selectAllBtn = document.querySelector('.select-all-btn');
            const options = taskFilter.options;

            // 检查是否所有选项都已选中
            const allSelected = Array.from(options).every(option => option.selected);

            if (allSelected) {
                // 取消全选
                Array.from(options).forEach(option => option.selected = false);
                selectAllBtn.textContent = t('visualization.selectAllTasks');
            } else {
                // 全选
                Array.from(options).forEach(option => option.selected = true);
                selectAllBtn.textContent = t('visualization.deselectAll') || '取消全选';
            }

            // 触发change事件
            taskFilter.dispatchEvent(new Event('change'));
        }

        // 监听任务选择变化，更新全选按钮状态
        function updateSelectAllButtonState() {
            const taskFilter = document.getElementById('task-filter');
            const selectAllBtn = document.querySelector('.select-all-btn');
            const options = taskFilter.options;

            if (!selectAllBtn) return;

            const allSelected = Array.from(options).every(option => option.selected);
            const noneSelected = Array.from(options).every(option => !option.selected);

            if (allSelected) {
                selectAllBtn.textContent = t('visualization.deselectAll') || '取消全选';
            } else {
                selectAllBtn.textContent = t('visualization.selectAllTasks');
            }
        }

        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (icon) {
                if (newTheme === 'dark') {
                    icon.textContent = '☀️';
                } else {
                    icon.textContent = '🌙';
                }
            }
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (icon && savedTheme === 'dark') {
                icon.textContent = '☀️';
            }
        }

        // 页面加载完成后初始化主题
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
        });
    </script>
</body>
</html>