<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management - Intelligent Document Analysis System</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <style>
        .document-management {
            padding: 15px;
        }

        .management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: var(--border-radius);
        }

        .search-filters {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 12px;
            margin-bottom: 15px;
            padding: 15px;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 8px 35px 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 13px;
            transition: var(--transition);
        }

        .search-box input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 14px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .filter-group label {
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .filter-group select {
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 13px;
            background: white;
        }
        
        .document-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 12px 15px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 3px;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 12px;
        }
        
        .document-list {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        
        .list-header {
            display: grid;
            grid-template-columns: 30px 2fr 100px 80px 80px 100px;
            gap: 10px;
            padding: 8px 15px;
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-secondary);
            border-bottom: 1px solid var(--border-color);
            font-size: 12px;
        }

        .document-item {
            display: grid;
            grid-template-columns: 30px 2fr 100px 80px 80px 100px;
            gap: 10px;
            padding: 8px 15px;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            align-items: center;
            font-size: 13px;
        }

        .document-item:hover {
            background: var(--bg-secondary);
        }

        .document-item:last-child {
            border-bottom: none;
        }

        .document-checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .document-title {
            font-weight: 500;
            color: var(--text-primary);
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .document-title:hover {
            color: var(--primary-color);
        }

        .document-meta {
            font-size: 11px;
            color: var(--text-muted);
            margin-top: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .document-date {
            color: var(--text-muted);
            font-size: 12px;
        }

        .document-words {
            color: var(--text-muted);
            font-size: 12px;
        }

        .document-type {
            display: inline-block;
            padding: 2px 6px;
            background: var(--primary-color);
            color: white;
            border-radius: 8px;
            font-size: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }
        
        .document-actions {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: var(--transition);
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-view {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .action-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            padding: 20px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .pagination button:hover:not(:disabled) {
            background: var(--primary-color);
            color: white;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .pagination .current-page {
            background: var(--primary-color);
            color: white;
        }
        
        .batch-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            padding: 10px 15px;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            align-items: center;
        }

        .batch-actions button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: var(--transition);
        }
        
        .btn-batch-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-batch-export {
            background: var(--success-color);
            color: white;
        }
        
        .selected-count {
            margin-left: auto;
            color: var(--text-muted);
            font-size: 14px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: var(--text-muted);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-muted);
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            color: var(--text-secondary);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.show {
            display: flex;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: var(--border-radius);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-muted);
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        .modal-footer button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: var(--transition);
        }
        
        .btn-cancel {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
        }
        
        .btn-confirm {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        /* 文档预览模态框样式 */
        .preview-modal-content {
            max-width: 800px;
            max-height: 90vh;
        }

        .preview-modal-body {
            max-height: 60vh;
            overflow-y: auto;
        }

        .preview-metadata {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .metadata-item {
            font-size: 14px;
            line-height: 1.4;
        }

        .metadata-item strong {
            color: var(--text-primary);
        }

        .preview-content-wrapper h4 {
            margin: 0 0 10px 0;
            color: var(--text-primary);
            font-size: 16px;
        }

        .preview-content {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            border: 1px solid var(--border-color);
        }

        /* 暗色主题下的预览样式 */
        [data-theme="dark"] .preview-content {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .preview-metadata {
            background: var(--bg-tertiary);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>📁 文档管理系统</h1>
            <p class="subtitle">智能文档管理与分析平台</p>
            <nav class="main-nav">
                <a href="/" class="nav-link" data-i18n="nav.home">🏠 Document Analysis</a>
                <a href="/static/visualization-en.html" class="nav-link" data-i18n="nav.visualization">📊 Data Visualization</a>
                <a href="/static/data-table.html" class="nav-link" data-i18n="nav.tableView">📋 Table View</a>
                <a href="/static/document-management-en.html" class="nav-link active" data-i18n="nav.documentManagement">📁 Document Management</a>
                <a href="/static/prompts-en.html" class="nav-link" data-i18n="nav.promptEditor">🔧 Prompt Editor</a>
                <a href="/docs" class="nav-link" data-i18n="nav.apiDocs">📚 API Documentation</a>
                <div class="language-selector">
                    <button type="button" data-lang="zh" onclick="switchLanguage('zh')">中文</button>
                    <button type="button" data-lang="en" onclick="switchLanguage('en')" class="active">English</button>
                </div>
                <button type="button" class="theme-toggle" onclick="toggleTheme()" data-i18n="analysis.switchTheme" title="Switch Theme">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main class="document-management">
            <!-- 管理头部 -->
            <div class="management-header">
                <div>
                    <h2>📁 文档管理</h2>
                    <p>管理您的所有文档，支持搜索、筛选、批量操作</p>
                </div>
                <button class="btn-primary" onclick="refreshDocuments()">🔄 刷新</button>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-filters">
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="搜索文档标题或内容...">
                    <button class="search-btn" onclick="searchDocuments()">🔍</button>
                </div>
                <div class="filter-group">
                    <label>排序方式</label>
                    <select id="sort-select" onchange="applySorting()">
                        <option value="created_at_desc">创建时间 (新到旧)</option>
                        <option value="created_at_asc">创建时间 (旧到新)</option>
                        <option value="title_asc">标题 (A-Z)</option>
                        <option value="title_desc">标题 (Z-A)</option>
                        <option value="word_count_desc">字数 (多到少)</option>
                        <option value="word_count_asc">字数 (少到多)</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>文档类型</label>
                    <select id="type-filter" onchange="applyFilters()">
                        <option value="">全部类型</option>
                        <option value="政策文件">政策文件</option>
                        <option value="研究报告">研究报告</option>
                        <option value="新闻报道">新闻报道</option>
                        <option value="学术论文">学术论文</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>

            <!-- 显示选项 -->
            <div class="display-options" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 8px 15px; background: var(--bg-secondary); border-radius: var(--border-radius); font-size: 12px;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label>每页显示:</label>
                    <select id="page-size-select" onchange="changePageSize()" style="padding: 4px 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;">
                        <option value="25">25</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                </div>
                <div id="total-count" style="color: var(--text-muted);">
                    共 0 个文档
                </div>
            </div>

            <!-- 文档统计 -->
            <div class="document-stats" id="document-stats">
                <div class="stat-card">
                    <div class="stat-number" id="total-docs">-</div>
                    <div class="stat-label">总文档数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-words">-</div>
                    <div class="stat-label">总字数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avg-words">-</div>
                    <div class="stat-label">平均字数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="recent-docs">-</div>
                    <div class="stat-label">本周新增</div>
                </div>
            </div>

            <!-- 批量操作 -->
            <div class="batch-actions" id="batch-actions" style="display: none;">
                <button class="btn-batch-delete" onclick="batchDelete()">🗑️ 批量删除</button>
                <button class="btn-batch-export" onclick="batchExport()">📤 批量导出</button>
                <div class="selected-count" id="selected-count">已选择 0 个文档</div>
            </div>

            <!-- 文档列表 -->
            <div class="document-list">
                <div class="list-header">
                    <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                    <div>文档标题</div>
                    <div>创建时间</div>
                    <div>字数</div>
                    <div>类型</div>
                    <div>操作</div>
                </div>
                <div id="document-list-body">
                    <div class="loading">
                        <p>正在加载文档...</p>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination" id="pagination" style="display: none;">
                <button id="prev-page" onclick="previousPage()" disabled>上一页</button>
                <span id="page-info">第 1 页，共 1 页</span>
                <button id="next-page" onclick="nextPage()" disabled>下一页</button>
            </div>
        </main>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal" id="delete-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">确认删除</h3>
                <button class="modal-close" onclick="closeModal('delete-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <p id="delete-message">确定要删除这个文档吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" onclick="closeModal('delete-modal')">取消</button>
                <button class="btn-danger" onclick="confirmDelete()">删除</button>
            </div>
        </div>
    </div>

    <!-- 文档预览模态框 -->
    <div class="modal" id="preview-modal">
        <div class="modal-content preview-modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="preview-title" data-i18n="documentManagement.previewTitle">文档预览</h3>
                <button class="modal-close" onclick="closeModal('preview-modal')">&times;</button>
            </div>
            <div class="modal-body preview-modal-body">
                <div class="preview-metadata" id="preview-metadata">
                    <!-- 文档元数据将在这里显示 -->
                </div>
                <div class="preview-content-wrapper">
                    <h4 data-i18n="documentManagement.documentContent">文档内容</h4>
                    <div class="preview-content" id="preview-content">
                        <!-- 文档内容将在这里显示 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" onclick="closeModal('preview-modal')" data-i18n="documentManagement.close">关闭</button>
                <button class="btn-primary" id="analyze-document-btn" data-i18n="documentManagement.analyzeDocument">📊 分析此文档</button>
            </div>
        </div>
    </div>

    <script>
        // 设置默认语言为英文
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof switchLanguage === 'function') {
                switchLanguage('en');
            }
        });
    </script>
    <script src="/static/js/document-management.js"></script>
</body>
</html>
